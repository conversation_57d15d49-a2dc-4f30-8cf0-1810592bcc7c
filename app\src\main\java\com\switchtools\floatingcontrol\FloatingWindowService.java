package com.switchtools.floatingcontrol;

import android.app.Service;
import android.content.Intent;
import android.content.SharedPreferences;
import android.graphics.PixelFormat;
import android.os.Build;
import android.os.IBinder;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.Switch;
import android.widget.TextView;
import android.widget.Toast;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.FileWriter;
import java.io.IOException;

public class FloatingWindowService extends Service {
    
    private WindowManager windowManager;
    private View floatingView;
    private Switch switchJinle, switchSperfect, switchPerfect;
    
    // 文件路径
    private String JINLE_FILE_PATH;
    private String SPERFECT_FILE_PATH;
    private String currentPath = "/storage/emulated/0/Android/data/com.xipu.cwtqmx/files/";

    private static final String PREFS_NAME = "SwitchToolsPrefs";
    private static final String KEY_CUSTOM_PATH = "custom_path";

    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }

    @Override
    public void onCreate() {
        super.onCreate();
        
        // 加载保存的设置
        loadSettings();
        initFilePaths();
        
        // 创建悬浮窗
        createFloatingWindow();
    }
    
    private void loadSettings() {
        SharedPreferences prefs = getSharedPreferences(PREFS_NAME, MODE_PRIVATE);
        // 使用你手动复制成功的路径作为默认路径
        String workingPath = "/storage/emulated/0/Android/data/com.xipu.cwtqmx/files/";
        currentPath = prefs.getString(KEY_CUSTOM_PATH, workingPath);
    }


    
    private void initFilePaths() {
        JINLE_FILE_PATH = currentPath + "kaiguan_pandin.txt";
        SPERFECT_FILE_PATH = currentPath + "kaiguan_pinfeng.txt";
    }

    private void createFloatingWindow() {
        windowManager = (WindowManager) getSystemService(WINDOW_SERVICE);
        
        // 加载悬浮窗布局
        LayoutInflater inflater = LayoutInflater.from(this);
        floatingView = inflater.inflate(R.layout.floating_window_layout, null);
        
        // 初始化控件
        initViews();
        setupSwitchListeners();
        loadSwitchStates();
        
        // 设置悬浮窗参数
        WindowManager.LayoutParams params = new WindowManager.LayoutParams(
                WindowManager.LayoutParams.WRAP_CONTENT,
                WindowManager.LayoutParams.WRAP_CONTENT,
                Build.VERSION.SDK_INT >= Build.VERSION_CODES.O ?
                        WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY :
                        WindowManager.LayoutParams.TYPE_PHONE,
                WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE,
                PixelFormat.TRANSLUCENT);
        
        params.gravity = Gravity.TOP | Gravity.START;
        params.x = 0;
        params.y = 100;
        
        // 添加拖拽功能
        addDragFunctionality(params);
        
        // 显示悬浮窗
        windowManager.addView(floatingView, params);
    }
    
    private void initViews() {
        switchJinle = floatingView.findViewById(R.id.floating_switch_jinle);
        switchSperfect = floatingView.findViewById(R.id.floating_switch_sperfect);
        switchPerfect = floatingView.findViewById(R.id.floating_switch_perfect);

        // 关闭按钮
        Button btnClose = floatingView.findViewById(R.id.floating_btn_close);
        btnClose.setOnClickListener(v -> stopSelf());
    }
    
    private void setupSwitchListeners() {
        // 自动跳舞开关（独立）
        switchJinle.setOnCheckedChangeListener((buttonView, isChecked) -> {
            String content = isChecked ? "开" : "关";
            writeToFile(JINLE_FILE_PATH, content);
        });

        // SPERFECT类型开关（与PERFECT互斥）
        switchSperfect.setOnCheckedChangeListener((buttonView, isChecked) -> {
            if (isChecked) {
                // 开启SPERFECT时，关闭PERFECT
                switchPerfect.setChecked(false);
                writeToFile(SPERFECT_FILE_PATH, "SPERFECT");
            } else {
                // 关闭SPERFECT时，如果PERFECT也是关闭的，则清空文件
                if (!switchPerfect.isChecked()) {
                    writeToFile(SPERFECT_FILE_PATH, "");
                }
            }
        });

        // PERFECT类型开关（与SPERFECT互斥）
        switchPerfect.setOnCheckedChangeListener((buttonView, isChecked) -> {
            if (isChecked) {
                // 开启PERFECT时，关闭SPERFECT
                switchSperfect.setChecked(false);
                writeToFile(SPERFECT_FILE_PATH, "PERFECT");
            } else {
                // 关闭PERFECT时，如果SPERFECT也是关闭的，则清空文件
                if (!switchSperfect.isChecked()) {
                    writeToFile(SPERFECT_FILE_PATH, "");
                }
            }
        });


    }
    
    private void loadSwitchStates() {
        // 读取自动跳舞状态
        String jinleContent = readFromFile(JINLE_FILE_PATH);
        switchJinle.setChecked("开".equals(jinleContent));

        // 读取类型选择状态
        String typeContent = readFromFile(SPERFECT_FILE_PATH);
        switchSperfect.setChecked("SPERFECT".equals(typeContent));
        switchPerfect.setChecked("PERFECT".equals(typeContent));


    }


    
    private void addDragFunctionality(WindowManager.LayoutParams params) {
        floatingView.setOnTouchListener(new View.OnTouchListener() {
            private int initialX, initialY;
            private float initialTouchX, initialTouchY;

            @Override
            public boolean onTouch(View v, MotionEvent event) {
                switch (event.getAction()) {
                    case MotionEvent.ACTION_DOWN:
                        initialX = params.x;
                        initialY = params.y;
                        initialTouchX = event.getRawX();
                        initialTouchY = event.getRawY();
                        return true;
                    case MotionEvent.ACTION_MOVE:
                        params.x = initialX + (int) (event.getRawX() - initialTouchX);
                        params.y = initialY + (int) (event.getRawY() - initialTouchY);
                        windowManager.updateViewLayout(floatingView, params);
                        return true;
                }
                return false;
            }
        });
    }
    
    private boolean writeToFile(String filePath, String content) {
        try {
            File file = new File(filePath);

            // 确保目录存在
            File parentDir = file.getParentFile();
            if (parentDir != null && !parentDir.exists()) {
                parentDir.mkdirs();
            }

            // 如果内容为空，处理关闭状态（删除文件）
            if (content == null || content.trim().isEmpty()) {
                return handleDisabledState(file);
            }

            // 有内容时，删除旧文件并创建新文件
            if (file.exists()) {
                if (!file.delete()) {
                    return false;
                }
            }

            // 创建新文件并写入内容
            if (file.createNewFile()) {
                try (FileWriter writer = new FileWriter(file)) {
                    writer.write(content);
                    writer.flush();
                }
                return true;
            } else {
                return false;
            }

        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    private boolean handleDisabledState(File file) {
        try {
            // 删除文件来表示功能关闭
            if (file.exists()) {
                return file.delete();
            } else {
                return true; // 文件本来就不存在，视为成功
            }
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }


    
    private String readFromFile(String filePath) {
        try {
            File file = new File(filePath);
            if (!file.exists()) {
                return "";
            }
            
            FileInputStream fis = new FileInputStream(file);
            byte[] buffer = new byte[(int) file.length()];
            fis.read(buffer);
            fis.close();
            
            return new String(buffer).trim();
        } catch (IOException e) {
            e.printStackTrace();
            return "";
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (floatingView != null) {
            windowManager.removeView(floatingView);
        }
    }
}
