# JDK 21 Android开发环境配置指南

## ✅ 你已安装的组件
- OpenJDK 21 (OpenJDK21U-jdk_x64_windows_hotspot_21.0.7_6.msi)

## 🔧 接下来需要完成的步骤

### 步骤1：配置JDK 21环境变量
```cmd
# 以管理员身份运行
setup-jdk21.bat
```

这个脚本会：
- 自动查找JDK 21安装路径
- 设置JAVA_HOME环境变量
- 更新PATH环境变量

### 步骤2：安装Android SDK
```cmd
# 以管理员身份运行
install-android-sdk.bat
```

这个脚本会：
- 下载Android命令行工具
- 设置Android环境变量
- 配置SDK路径

### 步骤3：安装Android组件
重新打开命令行窗口，运行：
```cmd
sdkmanager --licenses
sdkmanager "platform-tools" "platforms;android-34" "build-tools;34.0.0"
```

### 步骤4：编译APK
```cmd
build-apk.bat
```

---

## 🎯 快速安装流程

1. **以管理员身份运行命令行**
2. **运行配置脚本**：
   ```cmd
   setup-jdk21.bat
   install-android-sdk.bat
   ```
3. **重启命令行窗口**
4. **安装Android组件**：
   ```cmd
   sdkmanager --licenses
   sdkmanager "platform-tools" "platforms;android-34" "build-tools;34.0.0"
   ```
5. **编译APK**：
   ```cmd
   build-apk.bat
   ```

---

## 🔍 验证安装

运行以下命令检查环境：
```cmd
java -version          # 应显示 "21.0.7"
javac -version         # 应显示 "javac 21.0.7"
sdkmanager --version   # 应显示版本信息
```

---

## 📁 预期的目录结构

安装完成后应该有：
```
C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\  (JDK)
C:\Android\Sdk\                                          (Android SDK)
├── cmdline-tools\latest\
├── platform-tools\
├── platforms\android-34\
└── build-tools\34.0.0\
```

---

## 🔧 环境变量设置

应该设置的环境变量：
- `JAVA_HOME` = JDK安装路径
- `ANDROID_HOME` = `C:\Android\Sdk`
- `ANDROID_SDK_ROOT` = `C:\Android\Sdk`
- `PATH` 包含：
  - `%JAVA_HOME%\bin`
  - `%ANDROID_HOME%\cmdline-tools\latest\bin`
  - `%ANDROID_HOME%\platform-tools`

---

## 🚨 常见问题

### 问题1：找不到java命令
**解决方案**：
1. 运行 `setup-jdk21.bat`
2. 重启命令行窗口
3. 检查环境变量是否正确设置

### 问题2：JDK路径找不到
**手动设置**：
1. 找到JDK安装路径（通常在 `C:\Program Files\Eclipse Adoptium\`）
2. 手动设置环境变量：
   ```cmd
   setx JAVA_HOME "C:\Program Files\Eclipse Adoptium\jdk-********-hotspot" /M
   ```

### 问题3：Android SDK下载失败
**手动下载**：
1. 访问：https://developer.android.com/studio#command-tools
2. 下载 "Command line tools only"
3. 解压到 `C:\Android\Sdk\cmdline-tools\latest\`

### 问题4：编译时内存不足
**解决方案**：
- 已在 `gradle.properties` 中设置了4GB内存
- 如果还不够，可以增加到 `-Xmx6144m`

---

## 🎉 JDK 21 的优势

使用JDK 21进行Android开发的好处：
- 更好的性能
- 更新的语言特性
- 更好的内存管理
- 长期支持版本（LTS）

项目已配置为使用Java 11兼容性，确保与Android构建系统完美兼容。
