@echo off
echo ========================================
echo           Android APK 编译脚本
echo ========================================
echo.

REM 检查是否以管理员身份运行
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo 错误：需要管理员权限
    echo 请右键点击此文件，选择"以管理员身份运行"
    pause
    exit /b 1
)

echo 第一步：检查Java环境...
java -version >nul 2>&1
if %errorLevel% neq 0 (
    echo 配置Java环境变量...
    
    REM 查找JDK安装路径
    set "JAVA_PATH="
    for /d %%i in ("C:\Program Files\Eclipse Adoptium\jdk-*") do (
        if exist "%%i\bin\java.exe" set "JAVA_PATH=%%i"
    )
    
    if "%JAVA_PATH%"=="" (
        echo 错误：未找到JDK安装路径
        echo 请确认JDK已正确安装
        pause
        exit /b 1
    )
    
    echo 找到JDK: %JAVA_PATH%
    setx JAVA_HOME "%JAVA_PATH%" /M
    setx PATH "%PATH%;%JAVA_PATH%\bin" /M
    
    echo Java环境已配置，请重新运行此脚本
    pause
    exit /b 0
) else (
    echo Java环境正常
)

echo.
echo 第二步：检查Android SDK...
if not exist "C:\Android\Sdk\cmdline-tools\latest\bin\sdkmanager.bat" (
    echo Android SDK未安装，请手动完成以下步骤：
    echo.
    echo 1. 访问：https://developer.android.com/studio#command-tools
    echo 2. 下载 "Command line tools only" for Windows
    echo 3. 解压到：C:\Android\Sdk\cmdline-tools\latest\
    echo 4. 运行以下命令：
    echo    setx ANDROID_HOME "C:\Android\Sdk" /M
    echo    setx PATH "%%PATH%%;C:\Android\Sdk\cmdline-tools\latest\bin" /M
    echo.
    pause
    exit /b 1
) else (
    echo Android SDK正常
)

echo.
echo 第三步：检查Android组件...
if not exist "C:\Android\Sdk\platforms\android-34" (
    echo 安装Android组件...
    call "C:\Android\Sdk\cmdline-tools\latest\bin\sdkmanager.bat" --licenses
    call "C:\Android\Sdk\cmdline-tools\latest\bin\sdkmanager.bat" "platform-tools" "platforms;android-34" "build-tools;34.0.0"
)

echo.
echo 第四步：开始编译APK...
cd /d "%~dp0"

if not exist "gradlew.bat" (
    echo 错误：未找到gradlew.bat文件
    echo 请确认在正确的项目目录中运行此脚本
    pause
    exit /b 1
)

echo 正在编译...
call gradlew.bat assembleDebug

if %errorLevel% equ 0 (
    echo.
    echo ========================================
    echo            编译成功！
    echo ========================================
    echo.
    echo APK文件位置：
    echo %~dp0app\build\outputs\apk\debug\app-debug.apk
    echo.
    if exist "app\build\outputs\apk\debug\app-debug.apk" (
        echo 文件大小：
        dir "app\build\outputs\apk\debug\app-debug.apk" | find "app-debug.apk"
        echo.
        echo 是否打开APK所在文件夹？ (Y/N)
        set /p choice=
        if /i "%choice%"=="Y" (
            explorer "app\build\outputs\apk\debug\"
        )
    )
) else (
    echo.
    echo ========================================
    echo            编译失败
    echo ========================================
    echo 请检查上面的错误信息
)

echo.
pause
