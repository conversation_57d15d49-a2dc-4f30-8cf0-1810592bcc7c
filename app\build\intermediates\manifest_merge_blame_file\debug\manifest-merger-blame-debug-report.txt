1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.switchtools.floatingcontrol"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="34" />
10
11    <!-- 文件读写权限 -->
12    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
12-->C:\apk\app\src\main\AndroidManifest.xml:6:5-81
12-->C:\apk\app\src\main\AndroidManifest.xml:6:22-78
13    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
13-->C:\apk\app\src\main\AndroidManifest.xml:7:5-80
13-->C:\apk\app\src\main\AndroidManifest.xml:7:22-77
14
15    <!-- Android 11+ 需要的权限 -->
16    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
16-->C:\apk\app\src\main\AndroidManifest.xml:10:5-11:40
16-->C:\apk\app\src\main\AndroidManifest.xml:10:22-79
17
18    <!-- 悬浮窗权限 -->
19    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
19-->C:\apk\app\src\main\AndroidManifest.xml:14:5-78
19-->C:\apk\app\src\main\AndroidManifest.xml:14:22-75
20
21    <permission
21-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\5bed316c9c43a8bc7d9832f0735edfcf\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
22        android:name="com.switchtools.floatingcontrol.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
22-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\5bed316c9c43a8bc7d9832f0735edfcf\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
23        android:protectionLevel="signature" />
23-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\5bed316c9c43a8bc7d9832f0735edfcf\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
24
25    <uses-permission android:name="com.switchtools.floatingcontrol.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
25-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\5bed316c9c43a8bc7d9832f0735edfcf\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
25-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\5bed316c9c43a8bc7d9832f0735edfcf\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
26
27    <application
27-->C:\apk\app\src\main\AndroidManifest.xml:16:5-39:19
28        android:allowBackup="true"
28-->C:\apk\app\src\main\AndroidManifest.xml:17:9-35
29        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
29-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\5bed316c9c43a8bc7d9832f0735edfcf\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
30        android:dataExtractionRules="@xml/data_extraction_rules"
30-->C:\apk\app\src\main\AndroidManifest.xml:18:9-65
31        android:debuggable="true"
32        android:extractNativeLibs="true"
33        android:fullBackupContent="@xml/backup_rules"
33-->C:\apk\app\src\main\AndroidManifest.xml:19:9-54
34        android:icon="@mipmap/ic_launcher"
34-->C:\apk\app\src\main\AndroidManifest.xml:20:9-43
35        android:label="@string/app_name"
35-->C:\apk\app\src\main\AndroidManifest.xml:21:9-41
36        android:requestLegacyExternalStorage="true"
36-->C:\apk\app\src\main\AndroidManifest.xml:22:9-52
37        android:theme="@style/Theme.SwitchTools" >
37-->C:\apk\app\src\main\AndroidManifest.xml:23:9-49
38        <activity
38-->C:\apk\app\src\main\AndroidManifest.xml:26:9-33:20
39            android:name="com.switchtools.floatingcontrol.MainActivity"
39-->C:\apk\app\src\main\AndroidManifest.xml:27:13-41
40            android:exported="true" >
40-->C:\apk\app\src\main\AndroidManifest.xml:28:13-36
41            <intent-filter>
41-->C:\apk\app\src\main\AndroidManifest.xml:29:13-32:29
42                <action android:name="android.intent.action.MAIN" />
42-->C:\apk\app\src\main\AndroidManifest.xml:30:17-69
42-->C:\apk\app\src\main\AndroidManifest.xml:30:25-66
43
44                <category android:name="android.intent.category.LAUNCHER" />
44-->C:\apk\app\src\main\AndroidManifest.xml:31:17-77
44-->C:\apk\app\src\main\AndroidManifest.xml:31:27-74
45            </intent-filter>
46        </activity>
47
48        <service
48-->C:\apk\app\src\main\AndroidManifest.xml:35:9-38:40
49            android:name="com.switchtools.floatingcontrol.FloatingWindowService"
49-->C:\apk\app\src\main\AndroidManifest.xml:36:13-50
50            android:enabled="true"
50-->C:\apk\app\src\main\AndroidManifest.xml:37:13-35
51            android:exported="false" />
51-->C:\apk\app\src\main\AndroidManifest.xml:38:13-37
52
53        <provider
53-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\9d13f2821db83ff8741569fc495dc1fe\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
54            android:name="androidx.startup.InitializationProvider"
54-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\9d13f2821db83ff8741569fc495dc1fe\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
55            android:authorities="com.switchtools.floatingcontrol.androidx-startup"
55-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\9d13f2821db83ff8741569fc495dc1fe\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
56            android:exported="false" >
56-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\9d13f2821db83ff8741569fc495dc1fe\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
57            <meta-data
57-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\9d13f2821db83ff8741569fc495dc1fe\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
58                android:name="androidx.emoji2.text.EmojiCompatInitializer"
58-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\9d13f2821db83ff8741569fc495dc1fe\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
59                android:value="androidx.startup" />
59-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\9d13f2821db83ff8741569fc495dc1fe\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
60            <meta-data
60-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\f02b453ef5be71686d0e62efd0793368\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:29:13-31:52
61                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
61-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\f02b453ef5be71686d0e62efd0793368\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:30:17-78
62                android:value="androidx.startup" />
62-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\f02b453ef5be71686d0e62efd0793368\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:31:17-49
63            <meta-data
63-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a800750bab64c6d8e8a81dda96e57ec\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
64                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
64-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a800750bab64c6d8e8a81dda96e57ec\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
65                android:value="androidx.startup" />
65-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a800750bab64c6d8e8a81dda96e57ec\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
66        </provider>
67
68        <receiver
68-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a800750bab64c6d8e8a81dda96e57ec\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
69            android:name="androidx.profileinstaller.ProfileInstallReceiver"
69-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a800750bab64c6d8e8a81dda96e57ec\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
70            android:directBootAware="false"
70-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a800750bab64c6d8e8a81dda96e57ec\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
71            android:enabled="true"
71-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a800750bab64c6d8e8a81dda96e57ec\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
72            android:exported="true"
72-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a800750bab64c6d8e8a81dda96e57ec\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
73            android:permission="android.permission.DUMP" >
73-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a800750bab64c6d8e8a81dda96e57ec\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
74            <intent-filter>
74-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a800750bab64c6d8e8a81dda96e57ec\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
75                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
75-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a800750bab64c6d8e8a81dda96e57ec\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
75-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a800750bab64c6d8e8a81dda96e57ec\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
76            </intent-filter>
77            <intent-filter>
77-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a800750bab64c6d8e8a81dda96e57ec\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
78                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
78-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a800750bab64c6d8e8a81dda96e57ec\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
78-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a800750bab64c6d8e8a81dda96e57ec\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
79            </intent-filter>
80            <intent-filter>
80-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a800750bab64c6d8e8a81dda96e57ec\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
81                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
81-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a800750bab64c6d8e8a81dda96e57ec\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
81-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a800750bab64c6d8e8a81dda96e57ec\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
82            </intent-filter>
83            <intent-filter>
83-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a800750bab64c6d8e8a81dda96e57ec\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
84                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
84-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a800750bab64c6d8e8a81dda96e57ec\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
84-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a800750bab64c6d8e8a81dda96e57ec\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
85            </intent-filter>
86        </receiver>
87    </application>
88
89</manifest>
