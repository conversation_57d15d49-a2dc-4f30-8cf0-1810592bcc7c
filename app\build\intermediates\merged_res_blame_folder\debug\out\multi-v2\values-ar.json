{"logs": [{"outputFile": "com.switchtools.floatingcontrol.app-mergeDebugResources-29:/values-ar/values-ar.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\5bed316c9c43a8bc7d9832f0735edfcf\\transformed\\core-1.9.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "106", "startColumns": "4", "startOffsets": "8711", "endColumns": "100", "endOffsets": "8807"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\636fe5f1319118333643bf6dca29e378\\transformed\\appcompat-1.6.1\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,317,424,506,607,721,801,880,971,1064,1156,1250,1350,1443,1538,1631,1722,1816,1895,2000,2098,2196,2304,2404,2507,2662,2759", "endColumns": "107,103,106,81,100,113,79,78,90,92,91,93,99,92,94,92,90,93,78,104,97,97,107,99,102,154,96,81", "endOffsets": "208,312,419,501,602,716,796,875,966,1059,1151,1245,1345,1438,1533,1626,1717,1811,1890,1995,2093,2191,2299,2399,2502,2657,2754,2836"}, "to": {"startLines": "10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,105", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "515,623,727,834,916,1017,1131,1211,1290,1381,1474,1566,1660,1760,1853,1948,2041,2132,2226,2305,2410,2508,2606,2714,2814,2917,3072,8629", "endColumns": "107,103,106,81,100,113,79,78,90,92,91,93,99,92,94,92,90,93,78,104,97,97,107,99,102,154,96,81", "endOffsets": "618,722,829,911,1012,1126,1206,1285,1376,1469,1561,1655,1755,1848,1943,2036,2127,2221,2300,2405,2503,2601,2709,2809,2912,3067,3164,8706"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\5a17fecd6844736e0e7c65155b39080f\\transformed\\material-1.10.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,465,543,619,703,795,878,979,1098,1175,1238,1329,1398,1465,1565,1628,1693,1754,1822,1884,1942,2056,2116,2177,2234,2307,2430,2511,2591,2739,2820,2901,3029,3118,3194,3247,3301,3367,3445,3525,3609,3691,3763,3837,3910,3980,4089,4180,4251,4341,4436,4510,4593,4686,4735,4816,4885,4971,5056,5118,5182,5245,5314,5423,5533,5630,5730,5787,5845", "endLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77", "endColumns": "12,77,75,83,91,82,100,118,76,62,90,68,66,99,62,64,60,67,61,57,113,59,60,56,72,122,80,79,147,80,80,127,88,75,52,53,65,77,79,83,81,71,73,72,69,108,90,70,89,94,73,82,92,48,80,68,85,84,61,63,62,68,108,109,96,99,56,57,79", "endOffsets": "460,538,614,698,790,873,974,1093,1170,1233,1324,1393,1460,1560,1623,1688,1749,1817,1879,1937,2051,2111,2172,2229,2302,2425,2506,2586,2734,2815,2896,3024,3113,3189,3242,3296,3362,3440,3520,3604,3686,3758,3832,3905,3975,4084,4175,4246,4336,4431,4505,4588,4681,4730,4811,4880,4966,5051,5113,5177,5240,5309,5418,5528,5625,5725,5782,5840,5920"}, "to": {"startLines": "2,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3169,3247,3323,3407,3499,3582,3683,3802,3879,3942,4033,4102,4169,4269,4332,4397,4458,4526,4588,4646,4760,4820,4881,4938,5011,5134,5215,5295,5443,5524,5605,5733,5822,5898,5951,6005,6071,6149,6229,6313,6395,6467,6541,6614,6684,6793,6884,6955,7045,7140,7214,7297,7390,7439,7520,7589,7675,7760,7822,7886,7949,8018,8127,8237,8334,8434,8491,8549", "endLines": "9,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104", "endColumns": "12,77,75,83,91,82,100,118,76,62,90,68,66,99,62,64,60,67,61,57,113,59,60,56,72,122,80,79,147,80,80,127,88,75,52,53,65,77,79,83,81,71,73,72,69,108,90,70,89,94,73,82,92,48,80,68,85,84,61,63,62,68,108,109,96,99,56,57,79", "endOffsets": "510,3242,3318,3402,3494,3577,3678,3797,3874,3937,4028,4097,4164,4264,4327,4392,4453,4521,4583,4641,4755,4815,4876,4933,5006,5129,5210,5290,5438,5519,5600,5728,5817,5893,5946,6000,6066,6144,6224,6308,6390,6462,6536,6609,6679,6788,6879,6950,7040,7135,7209,7292,7385,7434,7515,7584,7670,7755,7817,7881,7944,8013,8122,8232,8329,8429,8486,8544,8624"}}]}]}