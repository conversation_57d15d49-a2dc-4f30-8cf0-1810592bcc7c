[{"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.switchtools.floatingcontrol.app-merged_res-31:\\drawable_floating_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.switchtools.floatingcontrol.app-main-33:\\drawable\\floating_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.switchtools.floatingcontrol.app-merged_res-31:\\drawable_score_button_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.switchtools.floatingcontrol.app-main-33:\\drawable\\score_button_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.switchtools.floatingcontrol.app-merged_res-31:\\xml_backup_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.switchtools.floatingcontrol.app-main-33:\\xml\\backup_rules.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.switchtools.floatingcontrol.app-merged_res-31:\\drawable_switch_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.switchtools.floatingcontrol.app-main-33:\\drawable\\switch_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.switchtools.floatingcontrol.app-merged_res-31:\\layout_activity_main.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.switchtools.floatingcontrol.app-main-33:\\layout\\activity_main.xml"}, {"merged": "com.switchtools.floatingcontrol.app-merged_res-31:/layout_activity_main.xml.flat", "source": "com.switchtools.floatingcontrol.app-main-33:/layout/activity_main.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.switchtools.floatingcontrol.app-merged_res-31:\\drawable_ic_launcher_foreground.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.switchtools.floatingcontrol.app-main-33:\\drawable\\ic_launcher_foreground.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.switchtools.floatingcontrol.app-merged_res-31:\\layout_floating_window_layout.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.switchtools.floatingcontrol.app-main-33:\\layout\\floating_window_layout.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.switchtools.floatingcontrol.app-merged_res-31:\\xml_data_extraction_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.switchtools.floatingcontrol.app-main-33:\\xml\\data_extraction_rules.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.switchtools.floatingcontrol.app-merged_res-31:\\mipmap-anydpi-v26_ic_launcher.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.switchtools.floatingcontrol.app-main-33:\\mipmap-anydpi-v26\\ic_launcher.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.switchtools.floatingcontrol.app-merged_res-31:\\drawable_ic_launcher_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.switchtools.floatingcontrol.app-main-33:\\drawable\\ic_launcher_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.switchtools.floatingcontrol.app-merged_res-31:\\drawable_close_button_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.switchtools.floatingcontrol.app-main-33:\\drawable\\close_button_background.xml"}]