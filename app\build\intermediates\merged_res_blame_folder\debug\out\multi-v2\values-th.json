{"logs": [{"outputFile": "com.switchtools.floatingcontrol.app-mergeDebugResources-29:/values-th/values-th.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\636fe5f1319118333643bf6dca29e378\\transformed\\appcompat-1.6.1\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,303,411,496,598,708,786,863,954,1047,1138,1232,1332,1425,1520,1614,1705,1796,1877,1980,2078,2176,2279,2385,2486,2639,2734", "endColumns": "104,92,107,84,101,109,77,76,90,92,90,93,99,92,94,93,90,90,80,102,97,97,102,105,100,152,94,81", "endOffsets": "205,298,406,491,593,703,781,858,949,1042,1133,1227,1327,1420,1515,1609,1700,1791,1872,1975,2073,2171,2274,2380,2481,2634,2729,2811"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,101", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "335,440,533,641,726,828,938,1016,1093,1184,1277,1368,1462,1562,1655,1750,1844,1935,2026,2107,2210,2308,2406,2509,2615,2716,2869,8315", "endColumns": "104,92,107,84,101,109,77,76,90,92,90,93,99,92,94,93,90,90,80,102,97,97,102,105,100,152,94,81", "endOffsets": "435,528,636,721,823,933,1011,1088,1179,1272,1363,1457,1557,1650,1745,1839,1930,2021,2102,2205,2303,2401,2504,2610,2711,2864,2959,8392"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\5bed316c9c43a8bc7d9832f0735edfcf\\transformed\\core-1.9.0\\res\\values-th\\values-th.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "102", "startColumns": "4", "startOffsets": "8397", "endColumns": "100", "endOffsets": "8493"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\5a17fecd6844736e0e7c65155b39080f\\transformed\\material-1.10.0\\res\\values-th\\values-th.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,285,358,430,513,598,684,783,896,976,1046,1136,1206,1266,1353,1419,1484,1545,1609,1670,1724,1825,1886,1946,2000,2070,2181,2268,2349,2492,2571,2653,2785,2877,2955,3009,3062,3128,3198,3276,3362,3442,3514,3592,3661,3730,3828,3910,3998,4091,4185,4259,4328,4423,4475,4558,4626,4711,4799,4861,4925,4988,5058,5158,5254,5351,5444,5502,5559", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,72,71,82,84,85,98,112,79,69,89,69,59,86,65,64,60,63,60,53,100,60,59,53,69,110,86,80,142,78,81,131,91,77,53,52,65,69,77,85,79,71,77,68,68,97,81,87,92,93,73,68,94,51,82,67,84,87,61,63,62,69,99,95,96,92,57,56,76", "endOffsets": "280,353,425,508,593,679,778,891,971,1041,1131,1201,1261,1348,1414,1479,1540,1604,1665,1719,1820,1881,1941,1995,2065,2176,2263,2344,2487,2566,2648,2780,2872,2950,3004,3057,3123,3193,3271,3357,3437,3509,3587,3656,3725,3823,3905,3993,4086,4180,4254,4323,4418,4470,4553,4621,4706,4794,4856,4920,4983,5053,5153,5249,5346,5439,5497,5554,5631"}, "to": {"startLines": "2,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2964,3037,3109,3192,3277,3363,3462,3575,3655,3725,3815,3885,3945,4032,4098,4163,4224,4288,4349,4403,4504,4565,4625,4679,4749,4860,4947,5028,5171,5250,5332,5464,5556,5634,5688,5741,5807,5877,5955,6041,6121,6193,6271,6340,6409,6507,6589,6677,6770,6864,6938,7007,7102,7154,7237,7305,7390,7478,7540,7604,7667,7737,7837,7933,8030,8123,8181,8238", "endLines": "5,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100", "endColumns": "12,72,71,82,84,85,98,112,79,69,89,69,59,86,65,64,60,63,60,53,100,60,59,53,69,110,86,80,142,78,81,131,91,77,53,52,65,69,77,85,79,71,77,68,68,97,81,87,92,93,73,68,94,51,82,67,84,87,61,63,62,69,99,95,96,92,57,56,76", "endOffsets": "330,3032,3104,3187,3272,3358,3457,3570,3650,3720,3810,3880,3940,4027,4093,4158,4219,4283,4344,4398,4499,4560,4620,4674,4744,4855,4942,5023,5166,5245,5327,5459,5551,5629,5683,5736,5802,5872,5950,6036,6116,6188,6266,6335,6404,6502,6584,6672,6765,6859,6933,7002,7097,7149,7232,7300,7385,7473,7535,7599,7662,7732,7832,7928,8025,8118,8176,8233,8310"}}]}]}