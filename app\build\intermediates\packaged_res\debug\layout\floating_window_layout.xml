<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="@drawable/floating_background"
    android:padding="10dp">

    <!-- 标题栏 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_marginBottom="10dp">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="开关工具"
            android:textSize="14sp"
            android:textStyle="bold"
            android:textColor="#333333" />

        <Button
            android:id="@+id/floating_btn_close"
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:text="—"
            android:textSize="14sp"
            android:textColor="#666666"
            android:background="@drawable/close_button_background"
            android:gravity="center" />

    </LinearLayout>

    <!-- 自动跳舞开关 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_marginBottom="8dp">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="自动跳舞"
            android:textSize="12sp"
            android:textColor="#333333" />

        <Switch
            android:id="@+id/floating_switch_jinle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="5dp"
            android:scaleX="0.8"
            android:scaleY="0.8" />

    </LinearLayout>

    <!-- SPERFECT开关 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_marginBottom="8dp">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="SPERFECT"
            android:textSize="12sp"
            android:textColor="#333333" />

        <Switch
            android:id="@+id/floating_switch_sperfect"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="5dp"
            android:scaleX="0.8"
            android:scaleY="0.8" />

    </LinearLayout>

    <!-- PERFECT开关 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_marginBottom="8dp">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="PERFECT"
            android:textSize="12sp"
            android:textColor="#333333" />

        <Switch
            android:id="@+id/floating_switch_perfect"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="5dp"
            android:scaleX="0.8"
            android:scaleY="0.8" />

    </LinearLayout>



</LinearLayout>
