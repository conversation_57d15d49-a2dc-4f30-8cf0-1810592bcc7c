# Android开发环境配置指南

## 📋 当前环境状态

### ✅ 已安装的开发环境
- **操作系统**：Windows
- **JDK版本**：OpenJDK 17 (Eclipse Adoptium jdk-*********-hotspot)
- **JDK安装路径**：`C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot`
- **Android SDK路径**：`C:\Android\Sdk`
- **工作目录**：`c:\apk`

### 🔧 环境变量配置
```
JAVA_HOME = C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot
ANDROID_HOME = C:\Android\Sdk
ANDROID_SDK_ROOT = C:\Android\Sdk
PATH 包含：
  - %JAVA_HOME%\bin
  - %ANDROID_HOME%\cmdline-tools\latest\bin
  - %ANDROID_HOME%\platform-tools
```

### 📦 已安装的Android组件
- **Platform Tools**：adb、fastboot等工具
- **Android API 34**：目标SDK版本
- **Build Tools 34.0.0**：编译工具
- **Command Line Tools**：sdkmanager等命令行工具

### 🎯 验证命令
```cmd
java -version          # 应显示 openjdk version "17.0.14"
sdkmanager --version   # 应显示版本信息
```

---

## 🚀 快速创建新Android项目

### 1. 项目初始化
```cmd
# 创建新项目目录
mkdir c:\新项目名
cd c:\新项目名

# 复制基础项目结构（从 c:\apk 复制）
# 或者手动创建以下结构：
```

### 2. 基础项目结构
```
新项目/
├── build.gradle                    # 项目配置
├── settings.gradle                 # 项目设置
├── gradle.properties              # Gradle属性
├── gradlew.bat                     # Windows编译脚本
├── gradle/wrapper/
│   ├── gradle-wrapper.properties  # Gradle版本配置
│   └── gradle-wrapper.jar         # Gradle包装器
└── app/
    ├── build.gradle               # 应用模块配置
    ├── proguard-rules.pro         # 混淆规则
    └── src/main/
        ├── AndroidManifest.xml    # 应用清单
        ├── java/包名/             # Java源码
        └── res/                   # 资源文件
            ├── layout/            # 布局文件
            ├── values/            # 值资源
            ├── drawable/          # 图片资源
            └── mipmap*/           # 应用图标
```

### 3. 关键配置文件模板

#### build.gradle (项目根目录)
```gradle
plugins {
    id 'com.android.application' version '8.1.2' apply false
}
```

#### app/build.gradle
```gradle
plugins {
    id 'com.android.application'
}

android {
    namespace '你的包名'
    compileSdk 34

    defaultConfig {
        applicationId "你的包名"
        minSdk 21
        targetSdk 34
        versionCode 1
        versionName "1.0"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }
}

dependencies {
    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'com.google.android.material:material:1.10.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
}
```

### 4. 编译命令
```cmd
# 设置Java环境（每次打开新命令行都需要）
$env:JAVA_HOME = "C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot"

# 编译APK
.\gradlew.bat assembleDebug

# APK输出位置
app\build\outputs\apk\debug\app-debug.apk
```

---

## 🔧 常用功能模块

### 文件操作
```java
// 读写文件的标准方法（已验证可用）
private boolean writeToFile(String filePath, String content) {
    FileOutputStream fos = null;
    try {
        File file = new File(filePath);
        File parentDir = file.getParentFile();
        if (parentDir != null && !parentDir.exists()) {
            parentDir.mkdirs();
        }
        
        fos = new FileOutputStream(file);
        fos.write(content.getBytes());
        fos.flush();
        return true;
    } catch (IOException e) {
        // 验证文件内容是否正确写入
        return verifyFileContent(filePath, content);
    } finally {
        if (fos != null) {
            try { fos.close(); } catch (IOException e) { /* 忽略关闭错误 */ }
        }
    }
}
```

### 权限申请
```xml
<!-- AndroidManifest.xml 常用权限 -->
<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
<uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
<uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
```

### 悬浮窗服务
```java
// 基础悬浮窗服务框架（可复用）
public class FloatingWindowService extends Service {
    // 参考 c:\apk\app\src\main\java\...\FloatingWindowService.java
}
```

---

## 🎯 开发流程

### 新项目开发步骤
1. **创建项目目录**
2. **复制基础结构**（从c:\apk复制并修改）
3. **修改包名和应用名**
4. **设计界面布局**
5. **实现业务逻辑**
6. **编译测试**
7. **打包发布**

### 调试技巧
- 使用`Toast.makeText()`显示调试信息
- 检查`updateStatus()`方法显示状态
- 查看文件是否正确创建和写入
- 使用`adb logcat`查看系统日志

---

## 🚨 常见问题解决

### 编译问题
- **Java找不到**：检查JAVA_HOME环境变量
- **SDK找不到**：检查ANDROID_HOME环境变量
- **权限错误**：以管理员身份运行命令行
- **网络问题**：使用国内镜像源

### 运行时问题
- **权限被拒绝**：检查AndroidManifest.xml权限声明
- **文件写入失败**：使用优化后的writeToFile方法
- **悬浮窗无法显示**：检查悬浮窗权限

---

## 📚 参考资源

### 成功案例
- **项目位置**：`c:\apk`
- **功能特点**：开关控制、文件操作、悬浮窗、数值调整
- **可复用组件**：文件操作、权限管理、悬浮窗框架

### 在线资源
- Android官方文档：https://developer.android.com/
- Material Design：https://material.io/
- 阿里云Maven镜像：https://maven.aliyun.com/

---

## 💡 AI助手使用指南

### 告知AI当前环境
```
我的Android开发环境已配置完成：
- JDK 17已安装在 C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot
- Android SDK已安装在 C:\Android\Sdk
- 工作目录在 c:\apk，有完整的项目模板
- 编译命令：$env:JAVA_HOME = "C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot"; .\gradlew.bat assembleDebug
```

### 请求AI帮助时
1. 说明要开发什么类型的应用
2. 提及可以复用c:\apk项目的结构
3. 重点关注业务逻辑实现
4. 不需要重新配置环境

---

*最后更新：2025年1月*
*环境状态：已验证可用*

我要开发一个新的Android应用，我的开发环境已经完全配置好：
- JDK 17 + Android SDK 已安装
- 有完整的项目模板在 c:\apk（包含文件操作、悬浮窗、权限管理等功能）
- 编译环境已验证可用
请帮我基于现有模板创建一个 [具体功能描述] 的应用。