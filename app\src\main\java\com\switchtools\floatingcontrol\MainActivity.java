package com.switchtools.floatingcontrol;

import android.Manifest;
import android.app.AlertDialog;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.content.SharedPreferences;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Environment;
import android.provider.Settings;
import android.widget.Button;
import android.widget.EditText;
import android.widget.Switch;
import android.widget.TextView;
import android.widget.Toast;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.FileWriter;
import java.io.IOException;

public class MainActivity extends AppCompatActivity {
    
    private Switch switchJinle;
    private Switch switchSperfect;
    private Switch switchPerfect;
    private TextView tvStatus;
    private TextView tvPath;
    private Button btnChangePath;
    private Button btnFloatingWindow;

    // 文件路径
    private String JINLE_FILE_PATH;
    private String SPERFECT_FILE_PATH;
    private String currentPath = "/storage/emulated/0/Android/data/com.xipu.cwtqmx/files/";

    private static final int PERMISSION_REQUEST_CODE = 100;
    private static final int OVERLAY_PERMISSION_REQUEST_CODE = 101;
    private static final String PREFS_NAME = "SwitchToolsPrefs";
    private static final String KEY_CUSTOM_PATH = "custom_path";

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);

        // 简单直接的初始化
        loadSavedPath();
        initFilePaths();
        initViews();
        checkPermissions();
        setupSwitchListeners();
        setupPathChanger();
        setupFloatingWindow();
        loadSwitchStates();
    }

    private void loadSavedPath() {
        SharedPreferences prefs = getSharedPreferences(PREFS_NAME, MODE_PRIVATE);
        // 使用你手动复制成功的路径作为默认路径
        String workingPath = "/storage/emulated/0/Android/data/com.xipu.cwtqmx/files/";
        currentPath = prefs.getString(KEY_CUSTOM_PATH, workingPath);
    }

    private void savePath(String path) {
        SharedPreferences prefs = getSharedPreferences(PREFS_NAME, MODE_PRIVATE);
        prefs.edit().putString(KEY_CUSTOM_PATH, path).apply();
        currentPath = path;
    }



    private void initFilePaths() {
        JINLE_FILE_PATH = currentPath + "kaiguan_pandin.txt";
        SPERFECT_FILE_PATH = currentPath + "kaiguan_pinfeng.txt";  // SPERFECT和PERFECT共用同一个文件
    }
    
    private void initViews() {
        switchJinle = findViewById(R.id.switch_jinle);
        switchSperfect = findViewById(R.id.switch_sperfect);
        switchPerfect = findViewById(R.id.switch_perfect);
        tvStatus = findViewById(R.id.tv_status);
        tvPath = findViewById(R.id.tv_path);
        btnChangePath = findViewById(R.id.btn_change_path);
        btnFloatingWindow = findViewById(R.id.btn_floating_window);

        // 显示当前路径
        tvPath.setText(currentPath);
    }
    
    private void checkPermissions() {
        // Android 11+ 需要特殊的存储权限
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            if (!Environment.isExternalStorageManager()) {
                updateStatus("需要申请所有文件访问权限");
                try {
                    Intent intent = new Intent(Settings.ACTION_MANAGE_APP_ALL_FILES_ACCESS_PERMISSION);
                    intent.setData(Uri.parse("package:" + getPackageName()));
                    startActivity(intent);
                    updateStatus("请在设置中开启所有文件访问权限");
                } catch (Exception e) {
                    updateStatus("无法打开权限设置，请手动开启");
                }
            } else {
                updateStatus("已获得所有文件访问权限");
            }
        } else {
            // Android 10 及以下的传统权限
            if (ContextCompat.checkSelfPermission(this, Manifest.permission.WRITE_EXTERNAL_STORAGE)
                    != PackageManager.PERMISSION_GRANTED) {
                ActivityCompat.requestPermissions(this,
                    new String[]{Manifest.permission.WRITE_EXTERNAL_STORAGE,
                               Manifest.permission.READ_EXTERNAL_STORAGE},
                    PERMISSION_REQUEST_CODE);
            } else {
                updateStatus("已获得存储权限");
            }
        }
    }


    
    private void setupSwitchListeners() {
        // 自动跳舞开关（独立）
        switchJinle.setOnCheckedChangeListener((buttonView, isChecked) -> {
            String content = isChecked ? "开" : "关";
            if (writeToFile(JINLE_FILE_PATH, content)) {
                updateStatus("自动跳舞: " + content);
            } else {
                updateStatus("自动跳舞写入失败");
                switchJinle.setChecked(!isChecked); // 恢复原状态
            }
        });

        // SPERFECT类型开关（与PERFECT互斥）
        switchSperfect.setOnCheckedChangeListener((buttonView, isChecked) -> {
            if (isChecked) {
                // 开启SPERFECT时，关闭PERFECT
                switchPerfect.setChecked(false);
                if (writeToFile(SPERFECT_FILE_PATH, "SPERFECT")) {
                    updateStatus("SPERFECT类型: 开启");
                } else {
                    updateStatus("SPERFECT写入失败");
                    switchSperfect.setChecked(false);
                }
            } else {
                // 关闭SPERFECT时，如果PERFECT也是关闭的，则清空文件
                if (!switchPerfect.isChecked()) {
                    writeToFile(SPERFECT_FILE_PATH, "");
                    updateStatus("类型选择: 关闭");
                }
            }
        });

        // PERFECT类型开关（与SPERFECT互斥）
        switchPerfect.setOnCheckedChangeListener((buttonView, isChecked) -> {
            if (isChecked) {
                // 开启PERFECT时，关闭SPERFECT
                switchSperfect.setChecked(false);
                if (writeToFile(SPERFECT_FILE_PATH, "PERFECT")) {
                    updateStatus("PERFECT类型: 开启");
                } else {
                    updateStatus("PERFECT写入失败");
                    switchPerfect.setChecked(false);
                }
            } else {
                // 关闭PERFECT时，如果SPERFECT也是关闭的，则清空文件
                if (!switchSperfect.isChecked()) {
                    writeToFile(SPERFECT_FILE_PATH, "");
                    updateStatus("类型选择: 关闭");
                }
            }
        });


    }
    
    private void loadSwitchStates() {
        // 读取自动跳舞状态
        String jinleContent = readFromFile(JINLE_FILE_PATH);
        switchJinle.setChecked("开".equals(jinleContent));

        // 读取类型选择状态
        String typeContent = readFromFile(SPERFECT_FILE_PATH);
        switchSperfect.setChecked("SPERFECT".equals(typeContent));
        switchPerfect.setChecked("PERFECT".equals(typeContent));



        updateStatus("状态加载完成");
    }

    private void setupPathChanger() {
        btnChangePath.setOnClickListener(v -> showPathDialog());
    }

    private void showPathDialog() {
        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        builder.setTitle("设置文件保存路径");

        final EditText input = new EditText(this);
        input.setText(currentPath);
        input.setHint("例如: /storage/emulated/0/Android/data/com.xipu.cwtqmx/files/");
        builder.setView(input);

        // 添加快速选择目标应用目录按钮
        builder.setNeutralButton("目标应用目录", (dialog, which) -> {
            String targetPath = "/storage/emulated/0/Android/data/com.xipu.cwtqmx/files/";
            savePath(targetPath);
            initFilePaths();
            tvPath.setText(targetPath);
            updateStatus("已切换到目标应用目录: " + targetPath);
            loadSwitchStates();
        });

        builder.setPositiveButton("确定", (dialog, which) -> {
            String newPath = input.getText().toString().trim();
            if (!newPath.isEmpty()) {
                if (!newPath.endsWith("/")) {
                    newPath += "/";
                }
                savePath(newPath);
                initFilePaths();
                tvPath.setText(newPath);
                updateStatus("路径已更新: " + newPath);
                loadSwitchStates(); // 重新加载开关状态
            }
        });

        builder.setNegativeButton("取消", null);
        builder.show();
    }



    private void setupFloatingWindow() {
        btnFloatingWindow.setOnClickListener(v -> {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                if (!Settings.canDrawOverlays(this)) {
                    Intent intent = new Intent(Settings.ACTION_MANAGE_OVERLAY_PERMISSION,
                            Uri.parse("package:" + getPackageName()));
                    startActivityForResult(intent, OVERLAY_PERMISSION_REQUEST_CODE);
                } else {
                    startFloatingWindowService();
                }
            } else {
                startFloatingWindowService();
            }
        });
    }

    private void startFloatingWindowService() {
        Intent intent = new Intent(this, FloatingWindowService.class);
        startService(intent);
        updateStatus("悬浮窗已开启");
        Toast.makeText(this, "悬浮窗已开启", Toast.LENGTH_SHORT).show();
    }
    
    private boolean writeToFile(String filePath, String content) {
        updateStatus("开始文件操作: " + filePath);

        // 方法1: 尝试新的文件创建方式
        if (tryCreateNewFile(filePath, content)) {
            return true;
        }

        // 方法2: 尝试传统的FileOutputStream方式
        if (tryTraditionalWrite(filePath, content)) {
            return true;
        }

        // 方法3: 尝试应用私有目录
        if (tryPrivateDirectory(filePath, content)) {
            return true;
        }

        updateStatus("所有文件写入方式都失败了");
        return false;
    }

    private boolean tryCreateNewFile(String filePath, String content) {
        try {
            File file = new File(filePath);
            updateStatus("方法1: 尝试新文件创建方式");

            // 确保目录存在
            File parentDir = file.getParentFile();
            if (parentDir != null && !parentDir.exists()) {
                boolean dirCreated = parentDir.mkdirs();
                updateStatus("创建目录: " + (dirCreated ? "成功" : "失败"));
                if (!dirCreated && !parentDir.exists()) {
                    updateStatus("目录创建失败，尝试下一种方法");
                    return false;
                }
            }

            // 如果内容为空，处理关闭状态（删除文件）
            if (content == null || content.trim().isEmpty()) {
                return handleDisabledState(file);
            }

            // 删除旧文件并创建新文件
            if (file.exists()) {
                if (!file.delete()) {
                    updateStatus("删除旧文件失败，尝试下一种方法");
                    return false;
                }
            }

            // 创建新文件并写入内容
            if (file.createNewFile()) {
                try (FileWriter writer = new FileWriter(file)) {
                    writer.write(content);
                    writer.flush();
                }
                updateStatus("方法1成功: " + file.getName() + " 内容: " + content);
                return true;
            }

        } catch (Exception e) {
            updateStatus("方法1失败: " + e.getMessage());
        }
        return false;
    }

    private boolean tryTraditionalWrite(String filePath, String content) {
        try {
            updateStatus("方法2: 尝试传统FileOutputStream方式");
            File file = new File(filePath);

            // 如果内容为空，删除文件
            if (content == null || content.trim().isEmpty()) {
                if (file.exists()) {
                    boolean deleted = file.delete();
                    updateStatus("方法2删除文件: " + (deleted ? "成功" : "失败"));
                    return deleted;
                }
                return true;
            }

            // 使用FileOutputStream写入
            try (FileOutputStream fos = new FileOutputStream(file)) {
                fos.write(content.getBytes());
                fos.flush();
                updateStatus("方法2成功: " + file.getName());
                return true;
            }

        } catch (Exception e) {
            updateStatus("方法2失败: " + e.getMessage());
        }
        return false;
    }

    private boolean tryPrivateDirectory(String filePath, String content) {
        try {
            updateStatus("方法3: 尝试应用私有目录");

            // 使用应用的外部私有目录
            File externalDir = getExternalFilesDir(null);
            if (externalDir == null) {
                updateStatus("无法获取外部私有目录");
                return false;
            }

            String fileName = new File(filePath).getName();
            File privateFile = new File(externalDir, fileName);

            // 如果内容为空，删除文件
            if (content == null || content.trim().isEmpty()) {
                if (privateFile.exists()) {
                    boolean deleted = privateFile.delete();
                    updateStatus("方法3删除文件: " + (deleted ? "成功" : "失败"));
                    return deleted;
                }
                return true;
            }

            // 写入私有目录
            try (FileWriter writer = new FileWriter(privateFile)) {
                writer.write(content);
                writer.flush();
                updateStatus("方法3成功(私有目录): " + privateFile.getAbsolutePath());

                // 尝试复制到目标位置
                try {
                    copyFile(privateFile, new File(filePath));
                    updateStatus("已复制到目标位置: " + filePath);
                } catch (Exception e) {
                    updateStatus("复制到目标位置失败，但私有目录写入成功");
                }
                return true;
            }

        } catch (Exception e) {
            updateStatus("方法3失败: " + e.getMessage());
        }
        return false;
    }

    private void copyFile(File source, File dest) throws IOException {
        try (FileInputStream fis = new FileInputStream(source);
             FileOutputStream fos = new FileOutputStream(dest)) {
            byte[] buffer = new byte[1024];
            int length;
            while ((length = fis.read(buffer)) > 0) {
                fos.write(buffer, 0, length);
            }
        }
    }

    private boolean handleDisabledState(File file) {
        try {
            // 删除文件来表示功能关闭
            if (file.exists()) {
                if (file.delete()) {
                    updateStatus("功能已关闭，文件已删除: " + file.getName());
                    return true;
                } else {
                    updateStatus("删除文件失败: " + file.getName());
                    return false;
                }
            } else {
                updateStatus("功能已关闭: " + file.getName());
                return true; // 文件本来就不存在，视为成功
            }
        } catch (Exception e) {
            updateStatus("关闭功能失败: " + e.getMessage());
            return false;
        }
    }



    private boolean verifyFileContent(String filePath, String expectedContent) {
        try {
            String actualContent = readFromFile(filePath);
            return expectedContent.equals(actualContent);
        } catch (Exception e) {
            return false;
        }
    }
    
    private String readFromFile(String filePath) {
        try {
            File file = new File(filePath);
            if (!file.exists()) {
                return ""; // 文件不存在返回空字符串
            }
            
            FileInputStream fis = new FileInputStream(file);
            byte[] buffer = new byte[(int) file.length()];
            fis.read(buffer);
            fis.close();
            
            return new String(buffer).trim();
        } catch (IOException e) {
            e.printStackTrace();
            Toast.makeText(this, "文件读取失败: " + e.getMessage(), Toast.LENGTH_SHORT).show();
            return "";
        }
    }
    
    private void updateStatus(String message) {
        tvStatus.setText("状态：" + message);
    }
    
    @Override
    public void onRequestPermissionsResult(int requestCode, String[] permissions, int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (requestCode == PERMISSION_REQUEST_CODE) {
            if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                Toast.makeText(this, "权限已授予", Toast.LENGTH_SHORT).show();
                updateStatus("权限已获取，可以正常使用");
                loadSwitchStates();
            } else {
                updateStatus("需要存储权限才能正常工作");
                Toast.makeText(this, "请在设置中手动开启存储权限", Toast.LENGTH_LONG).show();
            }
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == OVERLAY_PERMISSION_REQUEST_CODE) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                if (Settings.canDrawOverlays(this)) {
                    startFloatingWindowService();
                } else {
                    Toast.makeText(this, "需要悬浮窗权限才能开启悬浮窗", Toast.LENGTH_LONG).show();
                }
            }
        }
    }


}
