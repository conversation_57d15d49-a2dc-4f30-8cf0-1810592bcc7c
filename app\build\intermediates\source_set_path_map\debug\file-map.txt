com.switchtools.floatingcontrol.app-viewpager2-1.0.0-0 C:\Users\<USER>\.gradle\caches\transforms-3\14cb95eabc94d37081c3097595ba5258\transformed\viewpager2-1.0.0\res
com.switchtools.floatingcontrol.app-core-runtime-2.2.0-1 C:\Users\<USER>\.gradle\caches\transforms-3\1d8ece7401356e2a811481a88f599f23\transformed\core-runtime-2.2.0\res
com.switchtools.floatingcontrol.app-startup-runtime-1.1.1-2 C:\Users\<USER>\.gradle\caches\transforms-3\238209a1a12c9009989d74bd36857b62\transformed\startup-runtime-1.1.1\res
com.switchtools.floatingcontrol.app-savedstate-1.2.1-3 C:\Users\<USER>\.gradle\caches\transforms-3\246c802c87e70ec624b8b08636f4f906\transformed\savedstate-1.2.1\res
com.switchtools.floatingcontrol.app-transition-1.2.0-4 C:\Users\<USER>\.gradle\caches\transforms-3\38f5969b68457669aa719d3b543f2562\transformed\transition-1.2.0\res
com.switchtools.floatingcontrol.app-lifecycle-livedata-core-2.6.1-5 C:\Users\<USER>\.gradle\caches\transforms-3\41e2360b4583ef713aaf9935973c6dee\transformed\lifecycle-livedata-core-2.6.1\res
com.switchtools.floatingcontrol.app-recyclerview-1.1.0-6 C:\Users\<USER>\.gradle\caches\transforms-3\4ec406b6012a54af3c59888013dcb4d7\transformed\recyclerview-1.1.0\res
com.switchtools.floatingcontrol.app-material-1.10.0-7 C:\Users\<USER>\.gradle\caches\transforms-3\5a17fecd6844736e0e7c65155b39080f\transformed\material-1.10.0\res
com.switchtools.floatingcontrol.app-core-1.9.0-8 C:\Users\<USER>\.gradle\caches\transforms-3\5bed316c9c43a8bc7d9832f0735edfcf\transformed\core-1.9.0\res
com.switchtools.floatingcontrol.app-appcompat-1.6.1-9 C:\Users\<USER>\.gradle\caches\transforms-3\636fe5f1319118333643bf6dca29e378\transformed\appcompat-1.6.1\res
com.switchtools.floatingcontrol.app-lifecycle-livedata-2.6.1-10 C:\Users\<USER>\.gradle\caches\transforms-3\67740610ea8807dace459da2a141e473\transformed\lifecycle-livedata-2.6.1\res
com.switchtools.floatingcontrol.app-coordinatorlayout-1.1.0-11 C:\Users\<USER>\.gradle\caches\transforms-3\69b99573e1557a316392d47feb152875\transformed\coordinatorlayout-1.1.0\res
com.switchtools.floatingcontrol.app-constraintlayout-2.1.4-12 C:\Users\<USER>\.gradle\caches\transforms-3\6c63bafc2a4237618f85a073f48b9cda\transformed\constraintlayout-2.1.4\res
com.switchtools.floatingcontrol.app-emoji2-views-helper-1.2.0-13 C:\Users\<USER>\.gradle\caches\transforms-3\6d7b4acabeda9818751e436122607324\transformed\emoji2-views-helper-1.2.0\res
com.switchtools.floatingcontrol.app-annotation-experimental-1.3.0-14 C:\Users\<USER>\.gradle\caches\transforms-3\6f9e4b934423dffb7670d825108f1d31\transformed\annotation-experimental-1.3.0\res
com.switchtools.floatingcontrol.app-fragment-1.3.6-15 C:\Users\<USER>\.gradle\caches\transforms-3\760388c9f2ccf33c426f6a8b356ba368\transformed\fragment-1.3.6\res
com.switchtools.floatingcontrol.app-drawerlayout-1.1.1-16 C:\Users\<USER>\.gradle\caches\transforms-3\7e088e6e6b1d51e8ab8928d72a9e581b\transformed\drawerlayout-1.1.1\res
com.switchtools.floatingcontrol.app-profileinstaller-1.3.0-17 C:\Users\<USER>\.gradle\caches\transforms-3\8a800750bab64c6d8e8a81dda96e57ec\transformed\profileinstaller-1.3.0\res
com.switchtools.floatingcontrol.app-cardview-1.0.0-18 C:\Users\<USER>\.gradle\caches\transforms-3\93b82a20e71478f5ddfa231f35579a80\transformed\cardview-1.0.0\res
com.switchtools.floatingcontrol.app-lifecycle-viewmodel-2.6.1-19 C:\Users\<USER>\.gradle\caches\transforms-3\9ceb34c1127bd3d979db3fde74d95fd3\transformed\lifecycle-viewmodel-2.6.1\res
com.switchtools.floatingcontrol.app-emoji2-1.2.0-20 C:\Users\<USER>\.gradle\caches\transforms-3\9d13f2821db83ff8741569fc495dc1fe\transformed\emoji2-1.2.0\res
com.switchtools.floatingcontrol.app-activity-1.8.0-21 C:\Users\<USER>\.gradle\caches\transforms-3\a191470ae9d51b56224a0e1d66daeb59\transformed\activity-1.8.0\res
com.switchtools.floatingcontrol.app-core-ktx-1.9.0-22 C:\Users\<USER>\.gradle\caches\transforms-3\c45ef310f5cc5dcf4351d675adafe48e\transformed\core-ktx-1.9.0\res
com.switchtools.floatingcontrol.app-appcompat-resources-1.6.1-23 C:\Users\<USER>\.gradle\caches\transforms-3\effb1a5fd4607ee684df657355cf87f4\transformed\appcompat-resources-1.6.1\res
com.switchtools.floatingcontrol.app-lifecycle-process-2.6.1-24 C:\Users\<USER>\.gradle\caches\transforms-3\f02b453ef5be71686d0e62efd0793368\transformed\lifecycle-process-2.6.1\res
com.switchtools.floatingcontrol.app-lifecycle-viewmodel-savedstate-2.6.1-25 C:\Users\<USER>\.gradle\caches\transforms-3\f050673d21b2e59228eff6e47da1b98c\transformed\lifecycle-viewmodel-savedstate-2.6.1\res
com.switchtools.floatingcontrol.app-lifecycle-runtime-2.6.1-26 C:\Users\<USER>\.gradle\caches\transforms-3\f681b11c9eec4851813a34065a9016df\transformed\lifecycle-runtime-2.6.1\res
com.switchtools.floatingcontrol.app-pngs-27 C:\apk\app\build\generated\res\pngs\debug
com.switchtools.floatingcontrol.app-resValues-28 C:\apk\app\build\generated\res\resValues\debug
com.switchtools.floatingcontrol.app-packageDebugResources-29 C:\apk\app\build\intermediates\incremental\debug\packageDebugResources\merged.dir
com.switchtools.floatingcontrol.app-packageDebugResources-30 C:\apk\app\build\intermediates\incremental\debug\packageDebugResources\stripped.dir
com.switchtools.floatingcontrol.app-merged_res-31 C:\apk\app\build\intermediates\merged_res\debug
com.switchtools.floatingcontrol.app-debug-32 C:\apk\app\src\debug\res
com.switchtools.floatingcontrol.app-main-33 C:\apk\app\src\main\res
