@echo off
echo ===== 配置JDK 21环境 =====
echo.

echo 正在查找JDK 21安装路径...

REM 常见的JDK 21安装路径
set "JDK_PATHS=C:\Program Files\Eclipse Adoptium\jdk-********-hotspot;C:\Program Files\OpenJDK\jdk-********;C:\Program Files\Java\jdk-********;C:\Program Files\Eclipse Adoptium\jdk-21*;C:\Program Files\OpenJDK\jdk-21*"

set "JAVA_HOME_FOUND="

REM 检查每个可能的路径
for %%p in (%JDK_PATHS%) do (
    if exist "%%p\bin\java.exe" (
        set "JAVA_HOME_FOUND=%%p"
        echo 找到JDK: %%p
        goto :found
    )
)

REM 如果没找到，尝试通过注册表查找
echo 通过注册表查找JDK...
for /f "tokens=2*" %%a in ('reg query "HKLM\SOFTWARE\Eclipse Adoptium\JDK" /s /v Path 2^>nul ^| find "Path"') do (
    if exist "%%b\bin\java.exe" (
        set "JAVA_HOME_FOUND=%%b"
        echo 找到JDK: %%b
        goto :found
    )
)

echo 错误：未找到JDK 21安装路径
echo 请确认JDK已正确安装
echo 常见安装路径：
echo   C:\Program Files\Eclipse Adoptium\jdk-********-hotspot
echo   C:\Program Files\OpenJDK\jdk-********
pause
exit /b 1

:found
echo.
echo 设置JAVA_HOME环境变量...
setx JAVA_HOME "%JAVA_HOME_FOUND%" /M

echo 更新PATH环境变量...
REM 获取当前PATH
for /f "tokens=2*" %%a in ('reg query "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Environment" /v Path') do set "CURRENT_PATH=%%b"

REM 检查是否已包含Java路径
echo %CURRENT_PATH% | find "%JAVA_HOME_FOUND%\bin" >nul
if %errorLevel% == 0 (
    echo Java路径已在PATH中
) else (
    echo 添加Java到PATH...
    setx PATH "%CURRENT_PATH%;%JAVA_HOME_FOUND%\bin" /M
)

echo.
echo 设置完成！
echo JAVA_HOME = %JAVA_HOME_FOUND%
echo.
echo 请重新打开命令行窗口，然后运行以下命令验证：
echo   java -version
echo   javac -version
echo.
pause
