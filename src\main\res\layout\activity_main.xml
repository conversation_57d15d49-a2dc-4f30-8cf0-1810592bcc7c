<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="20dp"
    android:gravity="center">

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="开关工具"
        android:textSize="24sp"
        android:textStyle="bold"
        android:layout_marginBottom="40dp"
        android:gravity="center" />

    <!-- 劲乐模式开关 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_marginBottom="30dp"
        android:padding="15dp"
        android:background="@drawable/switch_background">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="劲乐模式"
            android:textSize="18sp"
            android:textColor="#333333" />

        <Switch
            android:id="@+id/switch_jinle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="10dp" />

    </LinearLayout>

    <!-- SPERFECT类型开关 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_marginBottom="30dp"
        android:padding="15dp"
        android:background="@drawable/switch_background">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="SPERFECT"
            android:textSize="18sp"
            android:textColor="#333333" />

        <Switch
            android:id="@+id/switch_sperfect"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="10dp" />

    </LinearLayout>

    <!-- 状态显示 -->
    <TextView
        android:id="@+id/tv_status"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="状态：正常"
        android:textSize="14sp"
        android:textColor="#666666"
        android:layout_marginTop="20dp" />

</LinearLayout>
