<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true">

<LinearLayout
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="16dp">

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="开关工具"
        android:textSize="20sp"
        android:textStyle="bold"
        android:layout_marginBottom="16dp"
        android:gravity="center"
        android:textColor="#333333" />

    <!-- 劲乐模式开关 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_marginBottom="12dp"
        android:padding="12dp"
        android:background="@drawable/switch_background">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="自动跳舞"
            android:textSize="16sp"
            android:textColor="#333333" />

        <Switch
            android:id="@+id/switch_jinle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="10dp" />

    </LinearLayout>

    <!-- SPERFECT类型开关 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_marginBottom="12dp"
        android:padding="12dp"
        android:background="@drawable/switch_background">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="SPERFECT"
            android:textSize="16sp"
            android:textColor="#333333" />

        <Switch
            android:id="@+id/switch_sperfect"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="10dp" />

    </LinearLayout>

    <!-- PERFECT类型开关 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_marginBottom="12dp"
        android:padding="12dp"
        android:background="@drawable/switch_background">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="PERFECT"
            android:textSize="16sp"
            android:textColor="#333333" />

        <Switch
            android:id="@+id/switch_perfect"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="10dp" />

    </LinearLayout>



    <!-- 悬浮窗按钮 -->
    <Button
        android:id="@+id/btn_floating_window"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="开启悬浮窗"
        android:textSize="16sp"
        android:layout_marginBottom="16dp"
        android:background="@drawable/switch_background"
        android:textColor="#333333" />

    <!-- 路径设置 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_marginTop="8dp"
        android:padding="12dp"
        android:background="@drawable/switch_background">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="文件保存路径："
            android:textSize="14sp"
            android:textColor="#333333" />

        <TextView
            android:id="@+id/tv_path"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="/storage/emulated/0/Android/data/com.xipu.cwtqmx/files/"
            android:textSize="12sp"
            android:textColor="#666666"
            android:layout_marginTop="4dp"
            android:background="#FFFFFF"
            android:padding="6dp" />

        <Button
            android:id="@+id/btn_change_path"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="更改路径"
            android:textSize="14sp"
            android:layout_marginTop="8dp" />

    </LinearLayout>

    <!-- 状态显示 -->
    <TextView
        android:id="@+id/tv_status"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="状态：正常"
        android:textSize="12sp"
        android:textColor="#666666"
        android:layout_marginTop="16dp"
        android:layout_marginBottom="16dp"
        android:gravity="center"
        android:background="#F5F5F5"
        android:padding="8dp" />

</LinearLayout>

</ScrollView>
