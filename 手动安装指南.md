# Android开发环境手动安装指南

## 📋 需要安装的组件
1. Java JDK 17
2. Android SDK命令行工具
3. 环境变量配置

---

## 🔧 步骤一：安装Java JDK

### 方法1：使用Chocolatey（推荐）
1. 以管理员身份运行PowerShell
2. 安装Chocolatey：
```powershell
Set-ExecutionPolicy Bypass -Scope Process -Force; [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072; iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))
```
3. 安装Java：
```powershell
choco install openjdk17 -y
```

### 方法2：手动下载安装
1. 访问：https://adoptium.net/
2. 下载Java 17 LTS for Windows x64
3. 运行安装程序，记住安装路径（通常是 `C:\Program Files\Eclipse Adoptium\jdk-17.x.x.x-hotspot\`）

---

## 🔧 步骤二：安装Android SDK

### 1. 创建SDK目录
```powershell
mkdir C:\Android\Sdk
```

### 2. 下载命令行工具
- 访问：https://developer.android.com/studio#command-tools
- 下载 "Command line tools only" for Windows
- 解压到 `C:\Android\Sdk\cmdline-tools\latest\`

### 3. 目录结构应该是：
```
C:\Android\Sdk\
└── cmdline-tools\
    └── latest\
        ├── bin\
        ├── lib\
        └── ...
```

---

## 🔧 步骤三：设置环境变量

### 1. 打开系统环境变量
- 按 `Win + R`，输入 `sysdm.cpl`
- 点击"环境变量"

### 2. 添加系统变量
添加以下变量：
- `ANDROID_HOME` = `C:\Android\Sdk`
- `ANDROID_SDK_ROOT` = `C:\Android\Sdk`
- `JAVA_HOME` = Java安装路径（如：`C:\Program Files\Eclipse Adoptium\jdk-**********-hotspot`）

### 3. 修改PATH变量
在系统PATH中添加：
- `%JAVA_HOME%\bin`
- `%ANDROID_HOME%\cmdline-tools\latest\bin`
- `%ANDROID_HOME%\platform-tools`

---

## 🔧 步骤四：安装Android SDK组件

重新打开PowerShell，运行：
```powershell
# 接受许可证
sdkmanager --licenses

# 安装必要组件
sdkmanager "platform-tools" "platforms;android-34" "build-tools;34.0.0"
```

---

## ✅ 验证安装

运行以下命令验证：
```powershell
java -version
sdkmanager --version
```

应该看到版本信息输出。

---

## 🚀 编译APK

环境配置完成后，在项目目录运行：
```powershell
cd C:\apk
.\gradlew.bat assembleDebug
```

编译完成后，APK文件位于：
`app\build\outputs\apk\debug\app-debug.apk`

---

## 🔧 故障排除

### 问题1：找不到java命令
- 检查JAVA_HOME环境变量
- 检查PATH中是否包含 `%JAVA_HOME%\bin`
- 重启PowerShell或重启电脑

### 问题2：找不到sdkmanager
- 检查ANDROID_HOME环境变量
- 检查cmdline-tools目录结构
- 确保PATH包含cmdline-tools\latest\bin

### 问题3：许可证未接受
```powershell
sdkmanager --licenses
```
输入 `y` 接受所有许可证

### 问题4：网络问题
如果下载失败，可以：
- 使用VPN
- 手动下载文件
- 使用国内镜像源
