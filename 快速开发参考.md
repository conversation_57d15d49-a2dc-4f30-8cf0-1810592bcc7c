# Android开发快速参考

## 🔧 环境信息
- **JDK**：OpenJDK 17 @ `C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot`
- **Android SDK**：`C:\Android\Sdk`
- **模板项目**：`c:\apk`（功能完整，可直接复用）

## ⚡ 快速编译
```cmd
$env:JAVA_HOME = "C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot"
.\gradlew.bat assembleDebug
```

## 📱 新项目创建
1. 复制 `c:\apk` 项目结构
2. 修改包名：`com.你的包名`
3. 修改应用名：`app/src/main/res/values/strings.xml`
4. 开始开发业务逻辑

## 🎯 可复用组件
- **文件操作**：`writeToFile()` 和 `readFromFile()` 方法
- **悬浮窗**：`FloatingWindowService.java` 完整框架
- **权限管理**：存储权限、悬浮窗权限申请逻辑
- **设置保存**：SharedPreferences使用模式
- **UI组件**：开关、按钮、对话框、滚动布局

## 🚀 告诉AI的话
```
我的Android开发环境已完全配置好：
- JDK 17 + Android SDK 已安装并配置
- 有完整的项目模板在 c:\apk
- 可以直接复用现有组件和结构
- 只需要专注于新功能的业务逻辑开发
```

## 📁 APK输出位置
`app\build\outputs\apk\debug\app-debug.apk`
