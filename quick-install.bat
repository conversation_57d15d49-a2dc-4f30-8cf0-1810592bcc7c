@echo off
echo ===== Android开发环境快速安装 =====
echo.

echo 正在检查管理员权限...
net session >nul 2>&1
if %errorLevel% == 0 (
    echo 管理员权限确认
) else (
    echo 错误：需要管理员权限
    echo 请右键点击此文件，选择"以管理员身份运行"
    pause
    exit /b 1
)

echo.
echo 1. 安装Chocolatey包管理器...
where choco >nul 2>&1
if %errorLevel% == 0 (
    echo Chocolatey已安装
) else (
    echo 正在安装Chocolatey...
    powershell -Command "Set-ExecutionPolicy Bypass -Scope Process -Force; [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072; iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))"
    call refreshenv
)

echo.
echo 2. 安装Java JDK 17...
choco install openjdk17 -y
call refreshenv

echo.
echo 3. 创建Android SDK目录...
if not exist "C:\Android\Sdk" mkdir "C:\Android\Sdk"

echo.
echo 4. 设置环境变量...
setx ANDROID_HOME "C:\Android\Sdk" /M
setx ANDROID_SDK_ROOT "C:\Android\Sdk" /M

echo.
echo 5. 下载Android命令行工具...
echo 请手动完成以下步骤：
echo 1. 访问：https://developer.android.com/studio#command-tools
echo 2. 下载 "Command line tools only" for Windows
echo 3. 解压到 C:\Android\Sdk\cmdline-tools\latest\
echo.

echo 安装完成！
echo 请重启电脑或重新打开命令行窗口
echo 然后运行以下命令验证：
echo   java -version
echo   sdkmanager --version
echo.
pause
