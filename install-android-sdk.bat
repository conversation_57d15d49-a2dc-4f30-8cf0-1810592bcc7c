@echo off
echo ===== 安装Android SDK =====
echo.

echo 检查Java环境...
java -version >nul 2>&1
if %errorLevel% == 0 (
    echo Java环境正常
    java -version
) else (
    echo 错误：Java未配置，请先运行 setup-jdk21.bat
    pause
    exit /b 1
)

echo.
echo 创建Android SDK目录...
set "ANDROID_SDK_ROOT=C:\Android\Sdk"
if not exist "%ANDROID_SDK_ROOT%" mkdir "%ANDROID_SDK_ROOT%"

echo.
echo 下载Android命令行工具...
set "CMDTOOLS_URL=https://dl.google.com/android/repository/commandlinetools-win-9477386_latest.zip"
set "TEMP_ZIP=%TEMP%\commandlinetools.zip"

echo 正在下载...
powershell -Command "Invoke-WebRequest -Uri '%CMDTOOLS_URL%' -OutFile '%TEMP_ZIP%'"

if not exist "%TEMP_ZIP%" (
    echo 下载失败，请检查网络连接
    echo 你也可以手动下载：
    echo %CMDTOOLS_URL%
    echo 然后解压到：%ANDROID_SDK_ROOT%\cmdline-tools\latest\
    pause
    exit /b 1
)

echo.
echo 解压命令行工具...
powershell -Command "Expand-Archive -Path '%TEMP_ZIP%' -DestinationPath '%ANDROID_SDK_ROOT%\cmdline-tools' -Force"

REM 重命名目录结构
if exist "%ANDROID_SDK_ROOT%\cmdline-tools\cmdline-tools" (
    move "%ANDROID_SDK_ROOT%\cmdline-tools\cmdline-tools" "%ANDROID_SDK_ROOT%\cmdline-tools\latest"
)

echo.
echo 设置Android环境变量...
setx ANDROID_HOME "%ANDROID_SDK_ROOT%" /M
setx ANDROID_SDK_ROOT "%ANDROID_SDK_ROOT%" /M

echo 更新PATH环境变量...
for /f "tokens=2*" %%a in ('reg query "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Environment" /v Path') do set "CURRENT_PATH=%%b"

REM 添加Android工具到PATH
set "PATHS_TO_ADD=%ANDROID_SDK_ROOT%\cmdline-tools\latest\bin;%ANDROID_SDK_ROOT%\platform-tools;%ANDROID_SDK_ROOT%\build-tools"
echo %CURRENT_PATH% | find "%ANDROID_SDK_ROOT%" >nul
if %errorLevel% == 0 (
    echo Android路径已在PATH中
) else (
    echo 添加Android工具到PATH...
    setx PATH "%CURRENT_PATH%;%PATHS_TO_ADD%" /M
)

echo.
echo 清理临时文件...
del "%TEMP_ZIP%" >nul 2>&1

echo.
echo ===== Android SDK安装完成 =====
echo.
echo 安装路径：%ANDROID_SDK_ROOT%
echo.
echo 请重新打开命令行窗口，然后运行：
echo   sdkmanager --version
echo   sdkmanager --licenses
echo   sdkmanager "platform-tools" "platforms;android-34" "build-tools;34.0.0"
echo.
pause
