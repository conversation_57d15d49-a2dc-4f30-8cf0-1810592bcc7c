@echo off
echo 下载Gradle Wrapper...

if not exist "gradle\wrapper" mkdir "gradle\wrapper"

echo 正在下载gradle-wrapper.jar...
powershell -Command "Invoke-WebRequest -Uri 'https://github.com/gradle/gradle/raw/v8.0.0/gradle/wrapper/gradle-wrapper.jar' -OutFile 'gradle\wrapper\gradle-wrapper.jar'"

if exist "gradle\wrapper\gradle-wrapper.jar" (
    echo Gradle Wrapper下载完成
) else (
    echo 下载失败，请检查网络连接
)

pause
