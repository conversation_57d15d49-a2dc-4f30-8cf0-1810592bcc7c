{"logs": [{"outputFile": "com.switchtools.floatingcontrol.app-mergeDebugResources-29:/values-gl/values-gl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\5bed316c9c43a8bc7d9832f0735edfcf\\transformed\\core-1.9.0\\res\\values-gl\\values-gl.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "102", "startColumns": "4", "startOffsets": "8869", "endColumns": "100", "endOffsets": "8965"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\636fe5f1319118333643bf6dca29e378\\transformed\\appcompat-1.6.1\\res\\values-gl\\values-gl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,313,421,506,607,735,821,902,994,1088,1185,1279,1379,1473,1569,1664,1756,1848,1929,2037,2144,2251,2360,2465,2579,2756,2855", "endColumns": "103,103,107,84,100,127,85,80,91,93,96,93,99,93,95,94,91,91,80,107,106,106,108,104,113,176,98,82", "endOffsets": "204,308,416,501,602,730,816,897,989,1083,1180,1274,1374,1468,1564,1659,1751,1843,1924,2032,2139,2246,2355,2460,2574,2751,2850,2933"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,101", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "325,429,533,641,726,827,955,1041,1122,1214,1308,1405,1499,1599,1693,1789,1884,1976,2068,2149,2257,2364,2471,2580,2685,2799,2976,8786", "endColumns": "103,103,107,84,100,127,85,80,91,93,96,93,99,93,95,94,91,91,80,107,106,106,108,104,113,176,98,82", "endOffsets": "424,528,636,721,822,950,1036,1117,1209,1303,1400,1494,1594,1688,1784,1879,1971,2063,2144,2252,2359,2466,2575,2680,2794,2971,3070,8864"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\5a17fecd6844736e0e7c65155b39080f\\transformed\\material-1.10.0\\res\\values-gl\\values-gl.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,275,356,436,521,623,719,824,957,1037,1115,1211,1290,1353,1448,1512,1581,1644,1718,1782,1838,1959,2017,2079,2135,2212,2351,2439,2519,2659,2739,2819,2968,3058,3139,3195,3251,3317,3396,3477,3565,3653,3732,3809,3891,3980,4081,4165,4257,4350,4451,4525,4617,4719,4771,4855,4921,5013,5101,5163,5227,5290,5360,5471,5576,5682,5781,5841,5901", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,80,79,84,101,95,104,132,79,77,95,78,62,94,63,68,62,73,63,55,120,57,61,55,76,138,87,79,139,79,79,148,89,80,55,55,65,78,80,87,87,78,76,81,88,100,83,91,92,100,73,91,101,51,83,65,91,87,61,63,62,69,110,104,105,98,59,59,84", "endOffsets": "270,351,431,516,618,714,819,952,1032,1110,1206,1285,1348,1443,1507,1576,1639,1713,1777,1833,1954,2012,2074,2130,2207,2346,2434,2514,2654,2734,2814,2963,3053,3134,3190,3246,3312,3391,3472,3560,3648,3727,3804,3886,3975,4076,4160,4252,4345,4446,4520,4612,4714,4766,4850,4916,5008,5096,5158,5222,5285,5355,5466,5571,5677,5776,5836,5896,5981"}, "to": {"startLines": "2,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3075,3156,3236,3321,3423,3519,3624,3757,3837,3915,4011,4090,4153,4248,4312,4381,4444,4518,4582,4638,4759,4817,4879,4935,5012,5151,5239,5319,5459,5539,5619,5768,5858,5939,5995,6051,6117,6196,6277,6365,6453,6532,6609,6691,6780,6881,6965,7057,7150,7251,7325,7417,7519,7571,7655,7721,7813,7901,7963,8027,8090,8160,8271,8376,8482,8581,8641,8701", "endLines": "5,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100", "endColumns": "12,80,79,84,101,95,104,132,79,77,95,78,62,94,63,68,62,73,63,55,120,57,61,55,76,138,87,79,139,79,79,148,89,80,55,55,65,78,80,87,87,78,76,81,88,100,83,91,92,100,73,91,101,51,83,65,91,87,61,63,62,69,110,104,105,98,59,59,84", "endOffsets": "320,3151,3231,3316,3418,3514,3619,3752,3832,3910,4006,4085,4148,4243,4307,4376,4439,4513,4577,4633,4754,4812,4874,4930,5007,5146,5234,5314,5454,5534,5614,5763,5853,5934,5990,6046,6112,6191,6272,6360,6448,6527,6604,6686,6775,6876,6960,7052,7145,7246,7320,7412,7514,7566,7650,7716,7808,7896,7958,8022,8085,8155,8266,8371,8477,8576,8636,8696,8781"}}]}]}