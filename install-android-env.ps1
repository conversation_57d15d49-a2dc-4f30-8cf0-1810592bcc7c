# Android开发环境自动安装脚本
# 需要管理员权限运行

Write-Host "=== Android开发环境安装脚本 ===" -ForegroundColor Green
Write-Host "正在检查和安装必要的开发环境..." -ForegroundColor Yellow

# 检查是否有管理员权限
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "错误：需要管理员权限运行此脚本" -ForegroundColor Red
    Write-Host "请右键点击PowerShell，选择'以管理员身份运行'" -ForegroundColor Yellow
    pause
    exit 1
}

# 检查并安装Chocolatey包管理器
Write-Host "1. 检查Chocolatey包管理器..." -ForegroundColor Cyan
if (!(Get-Command choco -ErrorAction SilentlyContinue)) {
    Write-Host "安装Chocolatey..." -ForegroundColor Yellow
    Set-ExecutionPolicy Bypass -Scope Process -Force
    [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072
    iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))
    refreshenv
} else {
    Write-Host "Chocolatey已安装" -ForegroundColor Green
}

# 安装Java JDK
Write-Host "2. 安装Java JDK 17..." -ForegroundColor Cyan
try {
    choco install openjdk17 -y
    Write-Host "Java JDK 17安装完成" -ForegroundColor Green
} catch {
    Write-Host "Java安装失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 刷新环境变量
refreshenv

# 安装Android SDK命令行工具
Write-Host "3. 下载Android SDK命令行工具..." -ForegroundColor Cyan

$androidSdkPath = "C:\Android\Sdk"
$cmdlineToolsPath = "$androidSdkPath\cmdline-tools\latest"

# 创建SDK目录
if (!(Test-Path $androidSdkPath)) {
    New-Item -ItemType Directory -Path $androidSdkPath -Force
}

# 下载命令行工具
$cmdlineToolsUrl = "https://dl.google.com/android/repository/commandlinetools-win-9477386_latest.zip"
$zipPath = "$env:TEMP\commandlinetools.zip"

try {
    Write-Host "下载Android命令行工具..." -ForegroundColor Yellow
    Invoke-WebRequest -Uri $cmdlineToolsUrl -OutFile $zipPath
    
    # 解压
    Write-Host "解压Android命令行工具..." -ForegroundColor Yellow
    Expand-Archive -Path $zipPath -DestinationPath "$androidSdkPath\cmdline-tools" -Force
    
    # 重命名目录
    if (Test-Path "$androidSdkPath\cmdline-tools\cmdline-tools") {
        Move-Item "$androidSdkPath\cmdline-tools\cmdline-tools" "$androidSdkPath\cmdline-tools\latest"
    }
    
    Write-Host "Android命令行工具安装完成" -ForegroundColor Green
} catch {
    Write-Host "Android SDK下载失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 设置环境变量
Write-Host "4. 设置环境变量..." -ForegroundColor Cyan

$env:ANDROID_HOME = $androidSdkPath
$env:ANDROID_SDK_ROOT = $androidSdkPath

# 永久设置环境变量
[Environment]::SetEnvironmentVariable("ANDROID_HOME", $androidSdkPath, "Machine")
[Environment]::SetEnvironmentVariable("ANDROID_SDK_ROOT", $androidSdkPath, "Machine")

# 添加到PATH
$currentPath = [Environment]::GetEnvironmentVariable("PATH", "Machine")
$pathsToAdd = @(
    "$androidSdkPath\cmdline-tools\latest\bin",
    "$androidSdkPath\platform-tools",
    "$androidSdkPath\build-tools"
)

foreach ($pathToAdd in $pathsToAdd) {
    if ($currentPath -notlike "*$pathToAdd*") {
        $newPath = "$currentPath;$pathToAdd"
        [Environment]::SetEnvironmentVariable("PATH", $newPath, "Machine")
    }
}

Write-Host "环境变量设置完成" -ForegroundColor Green

Write-Host "=== 安装完成 ===" -ForegroundColor Green
Write-Host "请重新启动PowerShell或重启电脑以使环境变量生效" -ForegroundColor Yellow
Write-Host "然后运行 'java -version' 和 'sdkmanager --version' 验证安装" -ForegroundColor Yellow

pause
