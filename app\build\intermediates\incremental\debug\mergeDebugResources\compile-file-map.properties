#Sun Jul 20 11:25:56 CST 2025
com.switchtools.floatingcontrol.app-main-33\:/drawable/score_button_background.xml=C\:\\apk\\app\\build\\intermediates\\merged_res\\debug\\drawable_score_button_background.xml.flat
com.switchtools.floatingcontrol.app-main-33\:/mipmap-anydpi-v26/ic_launcher.xml=C\:\\apk\\app\\build\\intermediates\\merged_res\\debug\\mipmap-anydpi-v26_ic_launcher.xml.flat
com.switchtools.floatingcontrol.app-main-33\:/xml/data_extraction_rules.xml=C\:\\apk\\app\\build\\intermediates\\merged_res\\debug\\xml_data_extraction_rules.xml.flat
com.switchtools.floatingcontrol.app-main-33\:/drawable/close_button_background.xml=C\:\\apk\\app\\build\\intermediates\\merged_res\\debug\\drawable_close_button_background.xml.flat
com.switchtools.floatingcontrol.app-main-33\:/layout/activity_main.xml=C\:\\apk\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_main.xml.flat
com.switchtools.floatingcontrol.app-main-33\:/drawable/ic_launcher_background.xml=C\:\\apk\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_launcher_background.xml.flat
com.switchtools.floatingcontrol.app-main-33\:/xml/backup_rules.xml=C\:\\apk\\app\\build\\intermediates\\merged_res\\debug\\xml_backup_rules.xml.flat
com.switchtools.floatingcontrol.app-main-33\:/drawable/ic_launcher_foreground.xml=C\:\\apk\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_launcher_foreground.xml.flat
com.switchtools.floatingcontrol.app-main-33\:/drawable/floating_background.xml=C\:\\apk\\app\\build\\intermediates\\merged_res\\debug\\drawable_floating_background.xml.flat
com.switchtools.floatingcontrol.app-main-33\:/drawable/switch_background.xml=C\:\\apk\\app\\build\\intermediates\\merged_res\\debug\\drawable_switch_background.xml.flat
com.switchtools.floatingcontrol.app-main-33\:/layout/floating_window_layout.xml=C\:\\apk\\app\\build\\intermediates\\merged_res\\debug\\layout_floating_window_layout.xml.flat
