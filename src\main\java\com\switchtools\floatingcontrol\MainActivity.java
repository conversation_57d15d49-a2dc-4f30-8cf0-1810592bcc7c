package com.switchtools.floatingcontrol;

import android.Manifest;
import android.content.pm.PackageManager;
import android.os.Bundle;
import android.os.Environment;
import android.widget.Switch;
import android.widget.TextView;
import android.widget.Toast;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;

public class MainActivity extends AppCompatActivity {
    
    private Switch switchJinle;
    private Switch switchSperfect;
    private TextView tvStatus;
    
    // 文件路径
    private static final String JINLE_FILE_PATH = "/storage/emulated/0/Android/data/com.switchtools.floatingcontrol/files/kaiguan_pandin.txt";
    private static final String SPERFECT_FILE_PATH = "/storage/emulated/0/Android/data/com.switchtools.floatingcontrol/files/kaiguan_pinfeng.txt";
    
    private static final int PERMISSION_REQUEST_CODE = 100;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);
        
        initViews();
        checkPermissions();
        setupSwitchListeners();
        loadSwitchStates();
    }
    
    private void initViews() {
        switchJinle = findViewById(R.id.switch_jinle);
        switchSperfect = findViewById(R.id.switch_sperfect);
        tvStatus = findViewById(R.id.tv_status);
    }
    
    private void checkPermissions() {
        if (ContextCompat.checkSelfPermission(this, Manifest.permission.WRITE_EXTERNAL_STORAGE) 
                != PackageManager.PERMISSION_GRANTED) {
            ActivityCompat.requestPermissions(this, 
                new String[]{Manifest.permission.WRITE_EXTERNAL_STORAGE, 
                           Manifest.permission.READ_EXTERNAL_STORAGE}, 
                PERMISSION_REQUEST_CODE);
        }
    }
    
    private void setupSwitchListeners() {
        switchJinle.setOnCheckedChangeListener((buttonView, isChecked) -> {
            String content = isChecked ? "开" : "关";
            if (writeToFile(JINLE_FILE_PATH, content)) {
                updateStatus("劲乐模式: " + content);
            } else {
                updateStatus("劲乐模式写入失败");
                switchJinle.setChecked(!isChecked); // 恢复原状态
            }
        });
        
        switchSperfect.setOnCheckedChangeListener((buttonView, isChecked) -> {
            String content = isChecked ? "SPERFECT" : "";
            if (writeToFile(SPERFECT_FILE_PATH, content)) {
                updateStatus("SPERFECT: " + (isChecked ? "开启" : "关闭"));
            } else {
                updateStatus("SPERFECT写入失败");
                switchSperfect.setChecked(!isChecked); // 恢复原状态
            }
        });
    }
    
    private void loadSwitchStates() {
        // 读取劲乐模式状态
        String jinleContent = readFromFile(JINLE_FILE_PATH);
        switchJinle.setChecked("开".equals(jinleContent));
        
        // 读取SPERFECT状态
        String sperfectContent = readFromFile(SPERFECT_FILE_PATH);
        switchSperfect.setChecked("SPERFECT".equals(sperfectContent));
        
        updateStatus("状态加载完成");
    }

    private boolean writeToFile(String filePath, String content) {
        try {
            File file = new File(filePath);

            // 确保目录存在
            File parentDir = file.getParentFile();
            if (parentDir != null && !parentDir.exists()) {
                parentDir.mkdirs();
            }

            // 写入文件
            FileOutputStream fos = new FileOutputStream(file);
            fos.write(content.getBytes());
            fos.close();

            return true;
        } catch (IOException e) {
            e.printStackTrace();
            Toast.makeText(this, "文件写入失败: " + e.getMessage(), Toast.LENGTH_SHORT).show();
            return false;
        }
    }

    private String readFromFile(String filePath) {
        try {
            File file = new File(filePath);
            if (!file.exists()) {
                return ""; // 文件不存在返回空字符串
            }

            FileInputStream fis = new FileInputStream(file);
            byte[] buffer = new byte[(int) file.length()];
            fis.read(buffer);
            fis.close();

            return new String(buffer).trim();
        } catch (IOException e) {
            e.printStackTrace();
            Toast.makeText(this, "文件读取失败: " + e.getMessage(), Toast.LENGTH_SHORT).show();
            return "";
        }
    }

    private void updateStatus(String message) {
        tvStatus.setText("状态：" + message);
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, String[] permissions, int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (requestCode == PERMISSION_REQUEST_CODE) {
            if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                Toast.makeText(this, "权限已授予", Toast.LENGTH_SHORT).show();
                loadSwitchStates();
            } else {
                Toast.makeText(this, "需要存储权限才能正常工作", Toast.LENGTH_LONG).show();
            }
        }
    }
}
