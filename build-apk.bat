@echo off
echo ===== 编译Android APK =====
echo.

echo 检查Java环境...
java -version >nul 2>&1
if %errorLevel% == 0 (
    echo Java环境正常
    java -version | findstr "21\."
    if %errorLevel% == 0 (
        echo 检测到JDK 21，配置正确
    ) else (
        echo 警告：建议使用JDK 21，当前版本可能不是最优选择
    )
) else (
    echo 错误：Java未安装或未配置
    echo 请先运行 setup-jdk21.bat 配置JDK 21环境
    pause
    exit /b 1
)

echo.
echo 检查Android SDK...
if exist "%ANDROID_HOME%\cmdline-tools\latest\bin\sdkmanager.bat" (
    echo Android SDK正常
) else (
    echo 错误：Android SDK未安装或未配置
    echo 请检查ANDROID_HOME环境变量和SDK安装
    pause
    exit /b 1
)

echo.
echo 安装必要的Android组件...
call "%ANDROID_HOME%\cmdline-tools\latest\bin\sdkmanager.bat" --licenses
call "%ANDROID_HOME%\cmdline-tools\latest\bin\sdkmanager.bat" "platform-tools" "platforms;android-34" "build-tools;34.0.0"

echo.
echo 开始编译APK...
call gradlew.bat assembleDebug

if %errorLevel% == 0 (
    echo.
    echo ===== 编译成功！ =====
    echo APK文件位置：app\build\outputs\apk\debug\app-debug.apk
    echo.
    if exist "app\build\outputs\apk\debug\app-debug.apk" (
        echo 文件大小：
        dir "app\build\outputs\apk\debug\app-debug.apk" | find "app-debug.apk"
    )
) else (
    echo.
    echo ===== 编译失败 =====
    echo 请检查错误信息
)

echo.
pause
